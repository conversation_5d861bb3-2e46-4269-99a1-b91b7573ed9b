import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { getChildrenAction } from "@/lib/actions/children";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import ChildrenList from "@/components/children/ChildrenList";

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export default async function ChildrenPage() {
  // Check if user is signed in on server side
  let session;
  let children = [];

  try {
    session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      redirect('/signin');
    }

    // Get children for the current user
    const result = await getChildrenAction();
    children = result.children || [];
  } catch (error) {
    console.error('Error in children page:', error);
    // During build time, redirect to signin
    redirect('/signin');
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-2">
              👶 Child Profiles
            </h1>
            <p className="text-sm sm:text-base text-gray-600">
              Manage your children&apos;s speech development profiles
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <Link href="/dashboard">
              <Button variant="outline" className="bg-white w-full sm:w-auto text-sm">
                ← Back to Dashboard
              </Button>
            </Link>
            <Link href="/dashboard/children/add">
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 w-full sm:w-auto text-sm">
                + Add Child
              </Button>
            </Link>
          </div>
        </div>

        {/* Children List or Empty State */}
        {children.length === 0 ? (
          <Card className="shadow-lg border-0 text-center py-8 sm:py-12">
            <CardContent className="px-4 sm:px-6">
              <div className="text-4xl sm:text-6xl mb-4">👶</div>
              <h3 className="text-xl sm:text-2xl font-semibold text-gray-800 mb-2">
                No children added yet
              </h3>
              <p className="text-sm sm:text-base text-gray-600 mb-6 max-w-md mx-auto">
                Start by adding your child&apos;s profile to begin tracking their speech development journey.
              </p>
              <Link href="/dashboard/children/add">
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-sm sm:text-base">
                  + Add Your First Child
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <ChildrenList children={children} />
        )}

        {/* Features Info */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50/60 via-blue-50/40 to-white hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-lg text-gray-800 flex items-center">
                📊 Level Assessment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm">
                Automatically determine your child&apos;s speech level (0-3) based on their age and vocabulary size.
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50/60 via-green-50/40 to-white hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-lg text-gray-800 flex items-center">
                📈 Progress Tracking
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm">
                Monitor vocabulary growth, exercise completion, and speech development milestones.
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-0 bg-gradient-to-br from-purple-50/60 via-purple-50/40 to-white hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-lg text-gray-800 flex items-center">
                🎯 Personalized Exercises
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm">
                Get exercises tailored to your child&apos;s specific level and learning needs.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
